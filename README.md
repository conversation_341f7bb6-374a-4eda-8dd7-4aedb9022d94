# AI-FDB

AI文件数据管理系统 - 基于AI技术的文件数据管理系统

## 项目概述

AI-FDB是一个基于AI技术的文件数据管理系统，核心目标是将非结构化电子文件转换为结构化数据，并支持自然语言查询。

## 当前版本

**v0.1** - 用户鉴权系统

## 技术栈

- **后端**: Spring Boot 3.x, MySQL 8.0, Redis
- **前端**: Vue 3, Element Plus, Pinia, TypeScript
- **OCR引擎**: PaddleOCR PP-OCRv5_server_rec
- **AI服务**: 阿里通义千问 qwen-turbo

## 快速开始

### 环境要求
- JDK 17+
- Node.js 18+
- MySQL 8.0
- Redis 6.0+

### 启动步骤
1. 启动数据库服务
2. 启动后端服务
3. 启动前端服务

详细文档请参考 [docs/README.md](docs/README.md)

## 项目结构

```
AI-FDB/
├── backend/                    # 后端项目
├── frontend/                   # 前端项目
├── scripts/                    # 脚本文件
├── docs/                       # 项目文档
├── docker-compose.yml          # Docker编排文件
└── README.md                   # 项目说明
```

## 开发指南

### 后端开发
```bash
cd backend
mvn spring-boot:run
```

### 前端开发
```bash
cd frontend
npm run dev
```

### 数据库初始化
```bash
mysql -u root -p < scripts/init-db.sql
```

## 版本计划

- **v0.1** - 用户鉴权系统 ✅
- **v0.2** - AI核心模块（PaddleOCR + qwen-turbo）
- **v0.3** - 工作空间管理
- **v0.4** - 数据表创建
- **v0.5** - 数据记录录入与语义抽取
- **v0.6** - AI数据问答与可视化

## 许可证

MIT License
