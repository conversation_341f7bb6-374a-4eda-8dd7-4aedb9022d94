# AI-FDB 后端API文档

## API概述

AI-FDB后端采用RESTful API设计，提供完整的用户认证、工作空间管理、数据表操作、文件处理和AI服务接口。

### 基础信息
- **Base URL**: `http://localhost:8080/api`
- **API版本**: v1
- **认证方式**: JWT <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
    "success": true,
    "message": "操作成功",
    "data": {},
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "uuid"
}
```

### 错误响应格式
```json
{
    "success": false,
    "error": {
        "code": "ERROR_CODE",
        "message": "错误描述",
        "details": "详细错误信息"
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "uuid"
}
```

## 认证相关API

### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "Test123456",
    "phone": "13800138000"
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "注册成功",
    "data": {
        "user": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>",
            "role": "user",
            "status": 1,
            "createdAt": "2024-01-01T00:00:00Z"
        }
    }
}
```

### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
    "usernameOrEmail": "testuser",
    "password": "Test123456",
    "rememberMe": true
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "登录成功",
    "data": {
        "user": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>",
            "role": "user"
        },
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "refresh_token_string",
        "expiresAt": "2024-01-02T00:00:00Z"
    }
}
```

### Token刷新
```http
POST /api/auth/refresh
Content-Type: application/json

{
    "refreshToken": "refresh_token_string"
}
```

### 用户登出
```http
POST /api/auth/logout
Authorization: Bearer {token}
```

### 获取当前用户信息
```http
GET /api/auth/profile
Authorization: Bearer {token}
```

## 用户管理API

### 更新用户信息
```http
PUT /api/users/profile
Authorization: Bearer {token}
Content-Type: application/json

{
    "username": "newusername",
    "email": "<EMAIL>",
    "phone": "13900139000",
    "avatarUrl": "http://example.com/avatar.jpg"
}
```

### 修改密码
```http
PUT /api/users/password
Authorization: Bearer {token}
Content-Type: application/json

{
    "currentPassword": "oldpassword",
    "newPassword": "newpassword"
}
```

### 获取用户列表（管理员）
```http
GET /api/users?page=0&size=20&search=keyword
Authorization: Bearer {admin_token}
```

## 工作空间管理API

### 获取工作空间列表
```http
GET /api/workspaces?page=0&size=10
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "content": [
            {
                "id": 1,
                "name": "我的工作空间",
                "description": "个人工作空间",
                "ownerId": 1,
                "isPublic": false,
                "tableCount": 5,
                "recordCount": 100,
                "createdAt": "2024-01-01T00:00:00Z",
                "updatedAt": "2024-01-01T00:00:00Z"
            }
        ],
        "totalElements": 1,
        "totalPages": 1,
        "size": 10,
        "number": 0
    }
}
```

### 创建工作空间
```http
POST /api/workspaces
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "新工作空间",
    "description": "工作空间描述",
    "isPublic": false
}
```

### 获取工作空间详情
```http
GET /api/workspaces/{workspaceId}
Authorization: Bearer {token}
```

### 更新工作空间
```http
PUT /api/workspaces/{workspaceId}
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "更新后的名称",
    "description": "更新后的描述"
}
```

### 删除工作空间
```http
DELETE /api/workspaces/{workspaceId}
Authorization: Bearer {token}
```

### 工作空间成员管理
```http
# 添加成员
POST /api/workspaces/{workspaceId}/members
Content-Type: application/json

{
    "userId": 2,
    "role": "editor"
}

# 获取成员列表
GET /api/workspaces/{workspaceId}/members

# 更新成员角色
PUT /api/workspaces/{workspaceId}/members/{userId}
Content-Type: application/json

{
    "role": "admin"
}

# 移除成员
DELETE /api/workspaces/{workspaceId}/members/{userId}
```

## 数据表管理API

### 获取数据表列表
```http
GET /api/workspaces/{workspaceId}/tables
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "合同管理表",
            "description": "存储合同信息",
            "workspaceId": 1,
            "fieldCount": 8,
            "recordCount": 25,
            "isTemplate": false,
            "createdBy": 1,
            "createdAt": "2024-01-01T00:00:00Z"
        }
    ]
}
```

### 创建数据表
```http
POST /api/workspaces/{workspaceId}/tables
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "合同管理表",
    "description": "存储合同关键信息",
    "fields": [
        {
            "fieldName": "contractName",
            "fieldType": "text",
            "isRequired": true,
            "extractionPrompt": "提取合同名称或标题"
        },
        {
            "fieldName": "contractAmount",
            "fieldType": "number",
            "isRequired": true,
            "extractionPrompt": "提取合同金额，只返回数字"
        },
        {
            "fieldName": "signDate",
            "fieldType": "date",
            "isRequired": false,
            "extractionPrompt": "提取签约日期，格式为YYYY-MM-DD"
        }
    ],
    "aiConfig": {
        "globalPrompt": "从合同文档中抽取关键信息",
        "confidenceThreshold": 0.8
    }
}
```

### AI生成数据表结构
```http
POST /api/workspaces/{workspaceId}/tables/ai-generate
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "采购合同表",
    "description": "用于管理公司采购合同的关键信息，包括供应商、金额、日期等"
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "suggestedFields": [
            {
                "fieldName": "supplierName",
                "fieldType": "text",
                "isRequired": true,
                "extractionPrompt": "提取供应商公司全称",
                "description": "供应商名称"
            },
            {
                "fieldName": "contractAmount",
                "fieldType": "number",
                "isRequired": true,
                "extractionPrompt": "提取合同总金额，只返回数字部分",
                "description": "合同金额"
            }
        ],
        "suggestedAiConfig": {
            "globalPrompt": "从采购合同文档中准确抽取关键业务信息",
            "confidenceThreshold": 0.85
        }
    }
}
```

### 获取数据表详情
```http
GET /api/tables/{tableId}
Authorization: Bearer {token}
```

### 更新数据表结构
```http
PUT /api/tables/{tableId}
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "更新后的表名",
    "description": "更新后的描述",
    "fields": [
        {
            "id": 1,
            "fieldName": "updatedField",
            "fieldType": "text",
            "isRequired": true
        }
    ]
}
```

### 删除数据表
```http
DELETE /api/tables/{tableId}
Authorization: Bearer {token}
```

## 数据记录管理API

### 获取记录列表
```http
GET /api/tables/{tableId}/records?page=0&size=20&search=keyword&sortBy=createdAt&sortDir=desc
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "content": [
            {
                "id": 1,
                "tableId": 1,
                "recordData": {
                    "contractName": "软件采购合同",
                    "contractAmount": 100000,
                    "signDate": "2024-01-01"
                },
                "sourceFileId": 1,
                "extractionConfidence": 0.95,
                "validationStatus": "valid",
                "createdBy": 1,
                "createdAt": "2024-01-01T00:00:00Z"
            }
        ],
        "totalElements": 1,
        "totalPages": 1
    }
}
```

### 手动创建记录
```http
POST /api/tables/{tableId}/records
Authorization: Bearer {token}
Content-Type: application/json

{
    "recordData": {
        "contractName": "手动录入合同",
        "contractAmount": 50000,
        "signDate": "2024-01-15"
    }
}
```

### AI抽取创建记录
```http
POST /api/tables/{tableId}/records/ai-extract
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [binary file data]
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "extractedData": {
            "contractName": {
                "value": "软件开发服务合同",
                "confidence": 0.95,
                "sourceText": "软件开发服务合同"
            },
            "contractAmount": {
                "value": 200000,
                "confidence": 0.88,
                "sourceText": "合同金额：人民币贰拾万元整"
            }
        },
        "overallConfidence": 0.91,
        "needsReview": false,
        "record": {
            "id": 2,
            "recordData": {
                "contractName": "软件开发服务合同",
                "contractAmount": 200000
            }
        }
    }
}
```

### 批量上传文件
```http
POST /api/tables/{tableId}/records/batch
Authorization: Bearer {token}
Content-Type: multipart/form-data

files: [multiple binary files]
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "taskId": "batch_task_123",
        "totalFiles": 10,
        "status": "processing",
        "estimatedTime": 300
    }
}
```

### 获取批量处理状态
```http
GET /api/tasks/{taskId}/status
Authorization: Bearer {token}
```

### 自然语言查询
```http
POST /api/tables/{tableId}/records/query
Authorization: Bearer {token}
Content-Type: application/json

{
    "query": "查找2024年金额大于10万的合同",
    "limit": 50
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "query": "查找2024年金额大于10万的合同",
        "parsedQuery": {
            "conditions": [
                {"field": "signDate", "operator": ">=", "value": "2024-01-01"},
                {"field": "signDate", "operator": "<=", "value": "2024-12-31"},
                {"field": "contractAmount", "operator": ">", "value": 100000}
            ]
        },
        "results": [
            {
                "id": 1,
                "recordData": {
                    "contractName": "软件采购合同",
                    "contractAmount": 200000,
                    "signDate": "2024-01-01"
                }
            }
        ],
        "totalCount": 1
    }
}
```

### 更新记录
```http
PUT /api/records/{recordId}
Authorization: Bearer {token}
Content-Type: application/json

{
    "recordData": {
        "contractName": "更新后的合同名称",
        "contractAmount": 150000
    }
}
```

### 删除记录
```http
DELETE /api/records/{recordId}
Authorization: Bearer {token}
```

## 文件管理API

### 上传文件
```http
POST /api/files/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [binary file data]
workspaceId: 1
tableId: 1 (可选)
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "fileId": 1,
        "originalName": "contract.pdf",
        "fileSize": 1024000,
        "fileType": "pdf",
        "uploadStatus": "completed",
        "processingStatus": "pending"
    }
}
```

### 获取文件信息
```http
GET /api/files/{fileId}
Authorization: Bearer {token}
```

### 下载文件
```http
GET /api/files/{fileId}/download
Authorization: Bearer {token}
```

### 获取文件处理状态
```http
GET /api/files/{fileId}/status
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "fileId": 1,
        "processingStatus": "completed",
        "ocrStatus": "completed",
        "aiStatus": "completed",
        "extractedText": "文档文本内容...",
        "ocrConfidence": 0.95,
        "processingTime": 15
    }
}
```

## AI服务API

### OCR文字识别
```http
POST /api/ai/ocr
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [binary file data]
options: {
    "language": "ch",
    "detectTable": true,
    "detectLayout": true
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "taskId": "ocr_task_123",
        "text": "识别出的文本内容",
        "confidence": 0.95,
        "textRegions": [
            {
                "bbox": [100, 100, 200, 150],
                "text": "文本内容",
                "confidence": 0.98
            }
        ],
        "tables": [
            {
                "bbox": [50, 200, 400, 350],
                "cells": [
                    {"row": 0, "col": 0, "text": "表头1"},
                    {"row": 0, "col": 1, "text": "表头2"}
                ]
            }
        ],
        "processingTime": 5.2
    }
}
```

### 语义抽取
```http
POST /api/ai/extract
Authorization: Bearer {token}
Content-Type: application/json

{
    "text": "合同文本内容...",
    "fields": [
        {
            "fieldName": "contractName",
            "extractionPrompt": "提取合同名称",
            "fieldType": "text"
        }
    ],
    "globalPrompt": "从合同中抽取关键信息"
}
```

### 自然语言转SQL
```http
POST /api/ai/query-parse
Authorization: Bearer {token}
Content-Type: application/json

{
    "query": "查找金额大于10万的合同",
    "tableSchema": {
        "tableName": "contracts",
        "fields": [
            {"name": "contractAmount", "type": "number"},
            {"name": "contractName", "type": "text"}
        ]
    }
}
```

## 系统管理API

### 获取系统配置
```http
GET /api/system/config
Authorization: Bearer {admin_token}
```

### 更新系统配置
```http
PUT /api/system/config
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "ai.ocr.engine": "paddleocr",
    "ai.llm.model": "qwen-turbo",
    "file.max_size": "104857600"
}
```

### 获取系统统计
```http
GET /api/system/stats
Authorization: Bearer {admin_token}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "userCount": 100,
        "workspaceCount": 50,
        "tableCount": 200,
        "recordCount": 10000,
        "fileCount": 5000,
        "storageUsed": "10GB",
        "aiTasksToday": 500
    }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| AUTH_001 | 用户名或密码错误 |
| AUTH_002 | Token已过期 |
| AUTH_003 | 权限不足 |
| USER_001 | 用户不存在 |
| USER_002 | 用户名已存在 |
| USER_003 | 邮箱已存在 |
| WORKSPACE_001 | 工作空间不存在 |
| WORKSPACE_002 | 无权限访问工作空间 |
| TABLE_001 | 数据表不存在 |
| TABLE_002 | 字段配置错误 |
| FILE_001 | 文件不存在 |
| FILE_002 | 文件格式不支持 |
| FILE_003 | 文件大小超限 |
| AI_001 | OCR处理失败 |
| AI_002 | AI抽取失败 |
| AI_003 | 模型服务不可用 |
| SYSTEM_001 | 系统内部错误 |

## 接口限流

### 限流规则
- 普通用户：100请求/分钟
- VIP用户：500请求/分钟
- 管理员：1000请求/分钟
- AI接口：20请求/分钟
- 文件上传：10请求/分钟

### 限流响应
```json
{
    "success": false,
    "error": {
        "code": "RATE_LIMIT_EXCEEDED",
        "message": "请求频率超限，请稍后重试",
        "retryAfter": 60
    }
}
```
