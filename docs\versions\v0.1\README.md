# AI-FDB v0.1 - 项目初始化与用户鉴权系统

## 版本概述

v0.1版本是AI-FDB系统的基础版本，专注于项目初始化和完整的用户认证功能。本版本搭建前后端分离的基础架构，实现用户注册、登录、JWT认证等核心功能，为后续版本奠定坚实基础。

## 🎯 核心目标

- **项目基础架构搭建** - 建立完整的开发环境和项目结构
- **用户认证系统** - 实现安全可靠的用户注册、登录、权限管理
- **前后端分离** - 建立标准的API接口和前端交互模式
- **数据库设计** - 设计用户相关的核心数据表结构
- **安全机制** - 实现JWT认证、密码加密、会话管理等安全功能

## 🎯 可视化验证目标

完成v0.1版本后，用户可以：
1. **访问系统首页** - 浏览器访问 `http://localhost:3000` 看到登录页面
2. **用户注册** - 填写注册表单，创建新用户账号
3. **用户登录** - 使用用户名/邮箱和密码登录系统
4. **查看仪表板** - 登录后进入个人仪表板页面
5. **个人信息管理** - 查看和编辑个人基本信息
6. **安全退出** - 点击退出按钮，清除登录状态

## 📚 文档索引

### 详细设计文档
- [项目结构设计](project-structure.md) - 完整的项目目录结构和组织方式
- [数据库设计](database-design.md) - 用户认证相关的数据表设计
- [后端实现](backend-implementation.md) - Spring Boot后端服务详细实现
- [前端实现](frontend-implementation.md) - Vue3前端应用详细实现

### 技术规范文档
- [API接口规范](../../backend-api.md) - 后端API接口设计规范
- [数据库规范](../../database-design.md) - 数据库设计规范
- [部署指南](../../deployment/README.md) - 系统部署和运维指南

## 📋 完整实现清单

### 项目初始化
- [x] 创建Spring Boot后端项目
- [x] 创建Vue 3前端项目
- [x] 配置数据库连接
- [x] 配置Redis连接
- [x] 设置跨域配置

### 数据库实现
- [x] 创建用户表结构
- [x] 创建会话表结构
- [x] 初始化数据库脚本
- [x] 数据库索引优化

### 后端实现
- [x] 用户注册API
- [x] 用户登录API
- [x] JWT认证中间件
- [x] 用户信息API
- [x] 密码加密服务

### 前端实现
- [x] 登录页面组件
- [x] 注册页面组件
- [x] 仪表板页面
- [x] 路由配置和守卫
- [x] 状态管理配置

### 部署配置
- [x] Docker配置文件
- [x] 环境变量配置
- [x] 启动脚本
- [x] 开发环境配置

## 功能模块

### 1. 用户认证
- 用户注册（邮箱/手机号验证）
- 用户登录（支持用户名/邮箱登录）
- 密码安全策略
- JWT Token认证
- 会话管理

### 2. 权限管理
- 基础角色系统（guest/user/admin）
- 登录状态维护
- Token自动刷新
- 安全退出

## 技术实现

### 后端技术栈
- Spring Boot 3.x
- Spring Security
- JWT (JSON Web Token)
- MySQL 8.0
- Redis (会话存储)
- BCrypt (密码加密)

### 前端技术栈
- Vue 3
- Element Plus
- Pinia (状态管理)
- Vue Router
- Axios (HTTP客户端)

## 数据库设计

### 核心表结构

#### users 表
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    status TINYINT DEFAULT 1,
    role ENUM('guest', 'user', 'admin') DEFAULT 'user',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### user_sessions 表
```sql
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    device_info JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## API接口

### 认证相关接口

#### 用户注册
```
POST /api/auth/register
Content-Type: application/json

{
  "username": "string",
  "email": "string",
  "password": "string",
  "phone": "string" (可选)
}
```

#### 用户登录
```
POST /api/auth/login
Content-Type: application/json

{
  "usernameOrEmail": "string",
  "password": "string",
  "rememberMe": boolean
}
```

#### Token刷新
```
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "string"
}
```

#### 用户登出
```
POST /api/auth/logout
Authorization: Bearer {token}
```

## 前端界面

### 页面结构
- 登录页面 (`/login`)
- 注册页面 (`/register`)
- 仪表板页面 (`/dashboard`) - 认证后的主页

### 组件设计
- LoginForm - 登录表单组件
- RegisterForm - 注册表单组件
- AuthGuard - 路由守卫组件
- UserMenu - 用户菜单组件

## 安全特性

### 密码安全
- 最小长度8位
- 必须包含大小写字母和数字
- BCrypt加密存储
- 登录失败次数限制

### Token安全
- JWT签名验证
- Token过期时间控制
- 刷新Token机制
- 设备信息记录

### 会话管理
- Redis存储会话信息
- 自动清理过期会话
- 并发登录控制
- IP地址验证

## 测试用例

### 单元测试
- 用户注册逻辑测试
- 密码加密验证测试
- JWT Token生成验证测试
- 权限验证测试

### 集成测试
- 注册流程端到端测试
- 登录流程端到端测试
- Token刷新流程测试
- 权限控制集成测试

### 安全测试
- SQL注入防护测试
- XSS攻击防护测试
- CSRF攻击防护测试
- 暴力破解防护测试

## 部署指南

### 环境要求
- JDK 17+
- Node.js 18+
- MySQL 8.0
- Redis 6.0+

### 配置文件
```yaml
# application.yml
spring:
  datasource:
    url: *************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}

jwt:
  secret: ${JWT_SECRET}
  expiration: 86400 # 24小时
  refresh-expiration: 604800 # 7天
```

### 启动步骤
1. 创建数据库和表结构
2. 配置环境变量
3. 启动Redis服务
4. 启动后端服务
5. 构建并部署前端

## 🚀 实施步骤

### 步骤1: 项目初始化
```bash
# 1. 创建项目目录
mkdir ai-fdb && cd ai-fdb
mkdir backend frontend

# 2. 初始化后端项目
cd backend
curl https://start.spring.io/starter.zip \
  -d dependencies=web,security,data-jpa,mysql,data-redis \
  -d javaVersion=17 \
  -d artifactId=ai-fdb-backend \
  -o backend.zip
unzip backend.zip && rm backend.zip

# 3. 初始化前端项目
cd ../frontend
npm create vue@latest ai-fdb-frontend
cd ai-fdb-frontend
npm install element-plus @element-plus/icons-vue
npm install pinia vue-router axios
```

### 步骤2: 数据库配置
```sql
-- 创建数据库
CREATE DATABASE aifdb_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户表
USE aifdb_dev;
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    status TINYINT DEFAULT 1,
    role ENUM('guest', 'user', 'admin') DEFAULT 'user',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- 创建会话表
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    device_info JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at)
);
```

### 步骤3: 后端核心代码实现
```java
// UserController.java - 用户控制器
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "http://localhost:3000")
public class UserController {

    @Autowired
    private UserService userService;

    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestBody RegisterRequest request) {
        try {
            User user = userService.register(request);
            return ResponseEntity.ok(new ApiResponse("注册成功", user));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse("注册失败: " + e.getMessage()));
        }
    }

    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody LoginRequest request) {
        try {
            LoginResponse response = userService.login(request);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse("登录失败: " + e.getMessage()));
        }
    }

    @GetMapping("/profile")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<?> getProfile(Authentication auth) {
        User user = userService.findByUsername(auth.getName());
        return ResponseEntity.ok(user);
    }
}
```

### 步骤4: 前端核心组件实现
```vue
<!-- LoginForm.vue - 登录表单组件 -->
<template>
  <div class="login-container">
    <el-card class="login-card">
      <h2>AI-FDB 登录</h2>
      <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef">
        <el-form-item prop="usernameOrEmail">
          <el-input
            v-model="loginForm.usernameOrEmail"
            placeholder="用户名或邮箱"
            prefix-icon="User"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            prefix-icon="Lock"
            show-password
          />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleLogin"
            :loading="loading"
            style="width: 100%"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
      <div class="login-footer">
        <router-link to="/register">还没有账号？立即注册</router-link>
      </div>
    </el-card>
  </div>
</template>
```

## 🧪 可视化验证指南

### 验证步骤1: 启动系统
```bash
# 1. 启动MySQL和Redis
docker-compose up -d mysql redis

# 2. 启动后端服务
cd backend
mvn spring-boot:run

# 3. 启动前端服务
cd frontend
npm run dev
```

### 验证步骤2: 功能测试
1. **访问首页**
   - 浏览器打开 `http://localhost:3000`
   - 应该看到登录页面

2. **用户注册**
   - 点击"立即注册"链接
   - 填写用户名、邮箱、密码
   - 点击注册按钮
   - 应该显示"注册成功"消息

3. **用户登录**
   - 返回登录页面
   - 输入刚注册的用户名和密码
   - 点击登录按钮
   - 应该跳转到仪表板页面

4. **查看个人信息**
   - 在仪表板页面点击用户头像
   - 应该显示个人信息页面
   - 可以查看和编辑基本信息

5. **安全退出**
   - 点击退出按钮
   - 应该返回登录页面
   - 尝试访问受保护页面应该被重定向到登录页

### 验证步骤3: API测试
```bash
# 1. 测试注册API
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "Test123456"
  }'

# 2. 测试登录API
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "usernameOrEmail": "testuser",
    "password": "Test123456"
  }'

# 3. 测试受保护API（使用返回的token）
curl -X GET http://localhost:8080/api/auth/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## ✅ 验收标准

### 功能验收
- [x] 用户可以成功注册账号
- [x] 用户可以使用用户名或邮箱登录
- [x] 登录后可以访问仪表板页面
- [x] 可以查看和编辑个人信息
- [x] 用户可以安全退出系统
- [x] 未登录用户无法访问受保护页面

### 技术验收
- [x] JWT Token认证机制正常工作
- [x] 登录状态可以正确维护
- [x] Token可以自动刷新
- [x] 密码安全策略生效
- [x] 会话管理功能正常
- [x] 所有API接口测试通过

### 界面验收
- [x] 前端界面响应式设计完成
- [x] 表单验证提示友好
- [x] 加载状态显示正常
- [x] 错误处理用户友好

### 安全验收
- [x] 密码BCrypt加密存储
- [x] JWT签名验证正常
- [x] 跨域配置安全
- [x] SQL注入防护测试通过