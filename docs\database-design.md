# AI-FDB 数据库设计文档

## 数据库架构概述

AI-FDB系统采用多数据库架构，针对不同类型的数据选择最适合的存储方案：

- **MySQL 8.0**: 存储结构化业务数据（用户、工作空间、数据表等）
- **MongoDB**: 存储非结构化数据（文档内容、OCR结果、AI分析结果）
- **Redis**: 缓存和会话存储
- **MinIO/本地存储**: 文件存储

## MySQL 数据库设计

### 数据库配置
```sql
-- 创建数据库
CREATE DATABASE aifdb_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE aifdb_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE aifdb_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'aifdb_user'@'%' IDENTIFIED BY 'aifdb_password';
GRANT ALL PRIVILEGES ON aifdb_*.* TO 'aifdb_user'@'%';
FLUSH PRIVILEGES;
```

### 核心表结构

#### 1. 用户管理表

##### users 表 - 用户基本信息
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    status TINYINT DEFAULT 1 COMMENT '状态: 1-正常 0-禁用',
    role ENUM('guest', 'user', 'admin') DEFAULT 'user' COMMENT '角色',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否验证',
    phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机是否验证',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    failed_login_count INT DEFAULT 0 COMMENT '失败登录次数',
    locked_until TIMESTAMP NULL COMMENT '锁定到期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT='用户表';
```

##### user_sessions 表 - 用户会话管理
```sql
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_token VARCHAR(255) UNIQUE NOT NULL COMMENT '会话令牌',
    refresh_token VARCHAR(255) UNIQUE NOT NULL COMMENT '刷新令牌',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    refresh_expires_at TIMESTAMP NOT NULL COMMENT '刷新令牌过期时间',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    device_info JSON COMMENT '设备信息',
    location_info JSON COMMENT '位置信息',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_refresh_token (refresh_token),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
) COMMENT='用户会话表';
```

#### 2. 工作空间管理表

##### workspaces 表 - 工作空间
```sql
CREATE TABLE workspaces (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '工作空间ID',
    name VARCHAR(100) NOT NULL COMMENT '工作空间名称',
    description TEXT COMMENT '描述',
    owner_id BIGINT NOT NULL COMMENT '所有者ID',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    settings JSON COMMENT '设置信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_owner_id (owner_id),
    INDEX idx_name (name),
    INDEX idx_is_public (is_public),
    INDEX idx_created_at (created_at)
) COMMENT='工作空间表';
```

##### workspace_members 表 - 工作空间成员
```sql
CREATE TABLE workspace_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '成员ID',
    workspace_id BIGINT NOT NULL COMMENT '工作空间ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role ENUM('owner', 'admin', 'editor', 'viewer') DEFAULT 'viewer' COMMENT '角色',
    permissions JSON COMMENT '权限配置',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_workspace_user (workspace_id, user_id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role (role)
) COMMENT='工作空间成员表';
```

#### 3. 数据表管理表

##### data_tables 表 - 数据表定义
```sql
CREATE TABLE data_tables (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '数据表ID',
    name VARCHAR(100) NOT NULL COMMENT '表名',
    description TEXT COMMENT '描述',
    workspace_id BIGINT NOT NULL COMMENT '工作空间ID',
    table_schema JSON NOT NULL COMMENT '表结构定义',
    ai_config JSON COMMENT 'AI抽取配置',
    validation_rules JSON COMMENT '验证规则',
    is_template BOOLEAN DEFAULT FALSE COMMENT '是否为模板',
    template_category VARCHAR(50) COMMENT '模板分类',
    record_count INT DEFAULT 0 COMMENT '记录数量',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_name (name),
    INDEX idx_is_template (is_template),
    INDEX idx_template_category (template_category),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
) COMMENT='数据表定义表';
```

##### table_fields 表 - 数据表字段定义
```sql
CREATE TABLE table_fields (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '字段ID',
    table_id BIGINT NOT NULL COMMENT '数据表ID',
    field_name VARCHAR(100) NOT NULL COMMENT '字段名',
    field_type ENUM('text', 'number', 'date', 'datetime', 'boolean', 'file', 'json') NOT NULL COMMENT '字段类型',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    is_unique BOOLEAN DEFAULT FALSE COMMENT '是否唯一',
    field_order INT DEFAULT 0 COMMENT '字段顺序',
    default_value TEXT COMMENT '默认值',
    extraction_prompt TEXT COMMENT 'AI抽取提示词',
    validation_rules JSON COMMENT '字段验证规则',
    display_config JSON COMMENT '显示配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    UNIQUE KEY uk_table_field (table_id, field_name),
    INDEX idx_table_id (table_id),
    INDEX idx_field_name (field_name),
    INDEX idx_field_type (field_type),
    INDEX idx_field_order (field_order)
) COMMENT='数据表字段定义表';
```

#### 4. 数据记录表

##### data_records 表 - 数据记录
```sql
CREATE TABLE data_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    table_id BIGINT NOT NULL COMMENT '数据表ID',
    record_data JSON NOT NULL COMMENT '记录数据',
    source_file_id BIGINT COMMENT '来源文件ID',
    extraction_confidence DECIMAL(3,2) COMMENT 'AI抽取置信度',
    extraction_metadata JSON COMMENT '抽取元数据',
    validation_status ENUM('pending', 'valid', 'invalid', 'manual_review') DEFAULT 'pending' COMMENT '验证状态',
    validation_errors JSON COMMENT '验证错误信息',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_source_file_id (source_file_id),
    INDEX idx_validation_status (validation_status),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
) COMMENT='数据记录表';
```

#### 5. 文件管理表

##### files 表 - 文件存储
```sql
CREATE TABLE files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    md5_hash VARCHAR(32) NOT NULL COMMENT 'MD5哈希',
    sha256_hash VARCHAR(64) COMMENT 'SHA256哈希',
    uploaded_by BIGINT NOT NULL COMMENT '上传者ID',
    workspace_id BIGINT COMMENT '工作空间ID',
    table_id BIGINT COMMENT '关联数据表ID',
    processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '处理状态',
    ocr_status ENUM('pending', 'processing', 'completed', 'failed', 'skipped') DEFAULT 'pending' COMMENT 'OCR状态',
    ai_status ENUM('pending', 'processing', 'completed', 'failed', 'skipped') DEFAULT 'pending' COMMENT 'AI处理状态',
    metadata JSON COMMENT '文件元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE SET NULL,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE SET NULL,
    INDEX idx_md5_hash (md5_hash),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_table_id (table_id),
    INDEX idx_processing_status (processing_status),
    INDEX idx_file_type (file_type),
    INDEX idx_created_at (created_at)
) COMMENT='文件存储表';
```

#### 6. AI任务管理表

##### ai_tasks 表 - AI处理任务
```sql
CREATE TABLE ai_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    task_type ENUM('ocr', 'extract', 'query', 'generate', 'analyze') NOT NULL COMMENT '任务类型',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    priority INT DEFAULT 5 COMMENT '优先级(1-10)',
    input_data JSON NOT NULL COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    error_message TEXT COMMENT '错误信息',
    processing_time INT COMMENT '处理时间(秒)',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retries INT DEFAULT 3 COMMENT '最大重试次数',
    file_id BIGINT COMMENT '关联文件ID',
    table_id BIGINT COMMENT '关联数据表ID',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    
    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE SET NULL,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_file_id (file_id),
    INDEX idx_table_id (table_id),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
) COMMENT='AI处理任务表';
```

#### 7. 系统配置表

##### system_configs 表 - 系统配置
```sql
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) COMMENT='系统配置表';
```

## MongoDB 文档设计

### 文档内容集合
```javascript
// document_contents 集合
{
    _id: ObjectId,
    file_id: Number,                    // 关联files表
    content_type: String,               // 'text', 'image', 'table', 'mixed'
    raw_content: String,                // 原始文本内容
    structured_content: {               // 结构化内容
        pages: [                        // 页面内容
            {
                page_number: Number,
                text: String,
                images: [String],       // 图片URL列表
                tables: [Object]        // 表格数据
            }
        ],
        metadata: {
            total_pages: Number,
            language: String,
            encoding: String
        }
    },
    ocr_result: {                       // OCR识别结果
        engine: String,                 // 'paddleocr'
        version: String,
        confidence: Number,
        processing_time: Number,
        result: {
            text_regions: [             // 文本区域
                {
                    bbox: [Number],     // 边界框坐标
                    text: String,
                    confidence: Number
                }
            ],
            tables: [                   // 表格识别结果
                {
                    bbox: [Number],
                    cells: [Object]
                }
            ]
        }
    },
    ai_analysis: {                      // AI分析结果
        model: String,                  // 'qwen-turbo'
        analysis_type: String,          // 'extraction', 'classification', 'summary'
        result: Object,
        confidence: Number,
        processing_time: Number
    },
    created_at: Date,
    updated_at: Date
}
```

### AI抽取历史集合
```javascript
// extraction_history 集合
{
    _id: ObjectId,
    task_id: Number,                    // 关联ai_tasks表
    file_id: Number,
    table_id: Number,
    extraction_rules: {                 // 抽取规则
        fields: [
            {
                field_name: String,
                extraction_prompt: String,
                field_type: String,
                is_required: Boolean
            }
        ],
        global_prompt: String
    },
    extracted_data: {                   // 抽取的数据
        field_name: {
            value: String,
            confidence: Number,
            source_text: String,
            bbox: [Number]              // 在文档中的位置
        }
    },
    confidence_scores: {                // 置信度分数
        overall: Number,
        by_field: Object
    },
    validation_result: {                // 验证结果
        is_valid: Boolean,
        errors: [String],
        warnings: [String]
    },
    created_at: Date
}
```

## Redis 缓存设计

### 缓存键命名规范
```
user:session:{session_token}        # 用户会话信息
user:profile:{user_id}              # 用户基本信息
workspace:{workspace_id}            # 工作空间信息
table:schema:{table_id}             # 数据表结构
ai:task:queue                       # AI任务队列
ocr:result:{file_id}                # OCR结果缓存
file:upload:{upload_id}             # 文件上传进度
```

### 缓存数据结构
```json
// 用户会话缓存
{
    "user_id": 123,
    "username": "testuser",
    "role": "user",
    "permissions": ["read", "write"],
    "workspace_ids": [1, 2, 3],
    "expires_at": "2024-12-31T23:59:59Z"
}

// AI任务队列
{
    "task_id": 456,
    "task_type": "extract",
    "priority": 5,
    "file_id": 789,
    "table_id": 101,
    "created_at": "2024-01-01T00:00:00Z"
}
```

## 数据库索引优化

### 复合索引设计
```sql
-- 用户会话查询优化
CREATE INDEX idx_user_sessions_user_active ON user_sessions(user_id, is_active, expires_at);

-- 数据记录查询优化
CREATE INDEX idx_data_records_table_created ON data_records(table_id, created_at DESC);

-- 文件处理状态查询优化
CREATE INDEX idx_files_status_type ON files(processing_status, file_type, created_at);

-- AI任务队列优化
CREATE INDEX idx_ai_tasks_queue ON ai_tasks(status, priority DESC, created_at);
```

## 数据备份策略

### MySQL备份
- 每日全量备份
- 每小时增量备份
- 二进制日志备份
- 主从复制

### MongoDB备份
- 每日快照备份
- Oplog备份
- 副本集配置

### Redis备份
- RDB快照备份
- AOF日志备份
- 主从复制

## 数据迁移脚本

### 初始化脚本
```sql
-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_public) VALUES
('system.name', 'AI-FDB', 'string', '系统名称', true),
('system.version', '0.1.0', 'string', '系统版本', true),
('ai.ocr.engine', 'paddleocr', 'string', 'OCR引擎', false),
('ai.llm.model', 'qwen-turbo', 'string', 'LLM模型', false),
('file.max_size', '104857600', 'number', '最大文件大小(100MB)', false),
('file.allowed_types', '["pdf","doc","docx","txt","jpg","png"]', 'json', '允许的文件类型', false);

-- 创建默认管理员用户
INSERT INTO users (username, email, password_hash, role, status, email_verified) VALUES
('admin', '<EMAIL>', '$2a$10$encrypted_password_hash', 'admin', 1, true);
```
